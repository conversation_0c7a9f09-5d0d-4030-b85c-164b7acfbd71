{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Link.vue", "mtime": 1754050582338}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { isExternal } from '@/utils/validate'\r\n\r\nexport default {\r\n  props: {\r\n    to: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  methods: {\r\n    linkProps(url) {\r\n      if (isExternal(url)) {\r\n        return {\r\n          is: 'a',\r\n          href: url,\r\n          target: '_blank',\r\n          rel: 'noopener'\r\n        }\r\n      }\r\n      return {\r\n        is: 'router-link',\r\n        to: url\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}