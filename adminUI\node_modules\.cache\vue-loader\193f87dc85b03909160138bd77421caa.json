{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Item.vue", "mtime": 1754050582338}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\r\nexport default {\r\n  name: 'MenuItem',\r\n  functional: true,\r\n  props: {\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  render(h, context) {\r\n    const { icon, title } = context.props\r\n    const vnodes = []\r\n\r\n    if (icon) {\r\n      const ic = 'el-icon-' + icon\r\n      vnodes.push(<i style=\"color:#ffffff;\" class={ic}/>)\r\n    }\r\n\r\n    if (title) {\r\n      vnodes.push(<span slot='title'>{(title)}</span>)\r\n    }\r\n    return vnodes\r\n  }\r\n}\r\n", null]}