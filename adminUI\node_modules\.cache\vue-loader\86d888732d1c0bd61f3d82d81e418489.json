{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\index.vue", "mtime": 1754558285923}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport * as roleApi from \"@/api/role.js\";\r\nimport edit from \"./edit\";\r\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\r\nexport default {\r\n  // name: \"index\"\r\n  components: { edit },\r\n  data() {\r\n    return {\r\n      constants: this.$constants,\r\n      listData: { list: [] },\r\n      listPram: {\r\n        createTime: null,\r\n        updateTime: null,\r\n        level: null,\r\n        page: 1,\r\n        limit: this.$constants.page.limit[0],\r\n        roleName: null,\r\n        rules: null,\r\n        status: null\r\n      },\r\n      menuList: [],\r\n      editDialogConfig: {\r\n        visible: false,\r\n        isCreate: 0, // 0=创建，1=编辑\r\n        editData: {}\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleGetRoleList();\r\n  },\r\n  methods: {\r\n    checkPermi,\r\n    handlerOpenDel(rowData) {\r\n      this.$confirm(this.$t(\"admin.system.role.confirmDelete\")).then(() => {\r\n        roleApi.delRole(rowData).then(data => {\r\n          this.$message.success(this.$t(\"admin.system.role.deleteSuccess\"));\r\n          this.handleGetRoleList();\r\n        });\r\n      });\r\n    },\r\n    handleGetRoleList() {\r\n      roleApi.getRoleList(this.listPram).catch(() => {\r\n        this.$message.error(this.$t(\"common.fetchDataFailed\"));\r\n      });\r\n    },\r\n    handlerOpenEdit(isCreate, editDate) {\r\n      isCreate === 1\r\n        ? (this.editDialogConfig.editData = editDate)\r\n        : (this.editDialogConfig.editData = {});\r\n      this.editDialogConfig.isCreate = isCreate;\r\n      this.editDialogConfig.visible = true;\r\n    },\r\n    hideEditDialog() {\r\n      this.editDialogConfig.visible = false;\r\n      this.handleGetRoleList();\r\n    },\r\n    handleSizeChange(val) {\r\n      this.listPram.limit = val;\r\n      this.handleGetRoleList(this.listPram);\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listPram.page = val;\r\n      this.handleGetRoleList(this.listPram);\r\n    },\r\n    //修改状态\r\n    handleStatusChange(row) {\r\n      roleApi.updateRoleStatus(row).then(res => {\r\n        this.$message.success(\"更新状态成功\");\r\n        this.handleGetRoleList();\r\n      });\r\n    },\r\n    resetQuery() {\r\n      this.listPram.roleName = \"\";\r\n      this.handleGetRoleList();\r\n    }\r\n  }\r\n};\r\n", null]}